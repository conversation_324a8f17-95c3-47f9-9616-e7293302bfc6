import { FileUtils } from "../../utils/file-utils.js";
import { StepExecutor } from "../init/services/step-executor.service.js";
import { UIService } from "../init/services/ui.service.js";
import { FeatureConfigService } from "./services/feature-config.service.js";

class FeatureCommand {
	constructor(
		private readonly featureConfigService: FeatureConfigService,
		private readonly uiService: UIService,
		private readonly stepExecutor: StepExecutor,
		private readonly featureName: string
	) {}

	async execute(): Promise<void> {
		const featureConfig = await this.featureConfigService.promptForFeatureConfig(this.featureName);
		const fileUtils = new FileUtils();

		const steps = [
			{
				name: "Criando estrutura da feature",
				action: () => {
					const basePath = `${featureConfig.path}/${featureConfig.name}`;
					fileUtils.createDir(basePath);

					if (featureConfig.includeComponents) {
						fileUtils.createDir(`${basePath}/components`);
					}
					if (featureConfig.includeHooks) {
						fileUtils.createDir(`${basePath}/hooks`);
					}
					if (featureConfig.includeServices) {
						fileUtils.createDir(`${basePath}/services`);
					}
					if (featureConfig.includeStyles) {
						fileUtils.createDir(`${basePath}/styles`);
					}
					if (featureConfig.includeTests) {
						fileUtils.createDir(`${basePath}/__tests__`);
					}
				},
			},
		];

		await this.stepExecutor.executeSteps(steps);
		this.uiService.showSuccessMessage();
	}
}

const featureCommand = async (name: string): Promise<void> => {
	const featureConfigService = new FeatureConfigService();
	const uiService = new UIService();
	const stepExecutor = new StepExecutor(uiService);
	const command = new FeatureCommand(featureConfigService, uiService, stepExecutor, name);
	return command.execute();
};

export default featureCommand;
