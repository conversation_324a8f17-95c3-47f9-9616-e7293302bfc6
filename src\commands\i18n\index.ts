import { FileUtils } from "../../utils/file-utils.js";
import { StepExecutor } from "../init/services/step-executor.service.js";
import { UIService } from "../init/services/ui.service.js";
import { I18nConfigService } from "./services/i18n-config.service.js";

class I18nCommand {
	constructor(
		private readonly i18nConfigService: I18nConfigService,
		private readonly uiService: UIService,
		private readonly stepExecutor: StepExecutor
	) {}

	async execute(): Promise<void> {
		const i18nConfig = await this.i18nConfigService.promptForI18nConfig();
		const fileUtils = new FileUtils();

		const steps = [
			{
				name: "Criando estrutura de internacionalização",
				action: () => {
					// Criar diretório base
					fileUtils.createDir(i18nConfig.path);

					// Criar diretórios para cada idioma
					const allLanguages = [i18nConfig.defaultLanguage, ...i18nConfig.languages];

					allLanguages.forEach(lang => {
						const langPath = `${i18nConfig.path}/${lang}`;
						fileUtils.createDir(langPath);

						// Criar arquivos de namespace para cada idioma
						i18nConfig.namespaces.forEach(namespace => {
							const template = {
								title: `${namespace} title`,
								description: `${namespace} description`,
								// Adicione mais chaves padrão conforme necessário
							};

							fileUtils.createFile(`${langPath}/${namespace}.json`, JSON.stringify(template, null, 2));
						});
					});

					// Criar arquivo de configuração next-i18next.config.js na raiz
					const i18nextConfig = `module.exports = {
  i18n: {
    defaultLocale: '${i18nConfig.defaultLanguage}',
    locales: ${JSON.stringify(allLanguages)},
  },
  localePath: '${i18nConfig.path}',
}`;

					fileUtils.createFile("next-i18next.config.js", i18nextConfig);
				},
			},
		];

		await this.stepExecutor.executeSteps(steps);
		this.uiService.showSuccessMessage();

		console.log("\n🌐 Configuração de internacionalização criada com sucesso!");
		console.log("\nPróximos passos:");
		console.log("1. Instale as dependências necessárias:");
		console.log("   npm install next-i18next");
		console.log("\n2. Configure o _app.tsx para usar o i18n:");
		console.log('   import { appWithTranslation } from "next-i18next";');
		console.log("   export default appWithTranslation(App);");
	}
}

const i18nCommand = async (): Promise<void> => {
	const i18nConfigService = new I18nConfigService();
	const uiService = new UIService();
	const stepExecutor = new StepExecutor(uiService);
	const command = new I18nCommand(i18nConfigService, uiService, stepExecutor);
	return command.execute();
};

export default i18nCommand;
