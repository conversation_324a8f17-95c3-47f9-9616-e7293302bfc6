import { FileUtils } from "../../utils/file-utils.js";
import { StepExecutor } from "../init/services/step-executor.service.js";
import { UIService } from "../init/services/ui.service.js";
import { SharedConfig, SharedItemType } from "./types.js";

class SharedCommand {
	constructor(
		private readonly uiService: UIService,
		private readonly stepExecutor: StepExecutor,
		private readonly type: SharedItemType,
		private readonly name: string
	) {}

	async execute(): Promise<void> {
		const sharedConfig: SharedConfig = {
			type: this.type,
			name: this.name,
		};

		const fileUtils = new FileUtils();

		const steps = [
			{
				name: `Criando ${sharedConfig.type} compartilhado`,
				action: () => {
					const basePath = `src/shared/${sharedConfig.type}s`;
					fileUtils.createDir(basePath);

					const fileName = `${basePath}/${sharedConfig.name}.tsx`;
					let content = "";

					switch (sharedConfig.type) {
						case "component":
							content =
								`import React, { ReactNode } from 'react';\n\n` +
								`type Props = {\n\tchildren?: ReactNode;\n};\n\n` +
								`export const ${sharedConfig.name} = ({ children }: Props) => {\n` +
								`\treturn (\n\t\t<div>{children}</div>\n\t);\n};`;
							break;
						case "hook":
							content =
								`import { useState } from 'react';\n\n` +
								`export const use${sharedConfig.name} = () => {\n` +
								`\tconst [state, setState] = useState();\n\n` +
								`\treturn { state, setState };\n};`;
							break;
						case "util":
							content = `export const ${sharedConfig.name} = () => {\n` + `\t// Add utility implementation here\n};`;
							break;
					}

					fileUtils.createFile(fileName, content);
				},
			},
		];

		await this.stepExecutor.executeSteps(steps);
		this.uiService.showSuccessMessage();
	}
}

const sharedCommand = async (type: SharedItemType, name: string): Promise<void> => {
	const uiService = new UIService();
	const stepExecutor = new StepExecutor(uiService);
	const command = new SharedCommand(uiService, stepExecutor, type, name);
	return command.execute();
};

export default sharedCommand;
