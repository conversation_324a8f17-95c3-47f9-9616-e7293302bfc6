import gradient from "gradient-string";

export default async function listCommand(): Promise<void> {
	const purpleGradient = gradient(["#6B48FF", "#DDA0DD"]);
	const greenGradient = gradient(["#1ABC9C", "#27AE60"]);

	console.log("\n📚 Comandos Disponíveis:\n");

	// Init Command
	console.log(purpleGradient("🎯 init"));
	console.log("   Cria a estrutura completa do projeto Next.js com configurações personalizadas");
	console.log("   Exemplo: next-gen init");
	console.log("   - Configuração interativa do projeto");
	console.log("   - Setup de diretórios e arquivos base");
	console.log("");

	// Module Command
	console.log(purpleGradient("🔧 module <nome>"));
	console.log("   Cria um novo módulo com estrutura completa");
	console.log("   Exemplo: next-gen module auth");
	console.log("   Opções interativas:");
	console.log("   - Components (/components)");
	console.log("   - Hooks (/hooks)");
	console.log("   - Services (/services)");
	console.log("");

	// Feature Command
	console.log(purpleGradient("🚀 feature <nome>"));
	console.log("   Cria uma nova feature com estrutura completa");
	console.log("   Exemplo: next-gen feature dashboard");
	console.log("   Opções interativas:");
	console.log("   - Components (/components)");
	console.log("   - Hooks (/hooks)");
	console.log("   - Services (/services)");
	console.log("   - Styles (/styles)");
	console.log("   - Tests (/__tests__)");
	console.log("");

	// Shared Command
	console.log(purpleGradient("🔄 shared <tipo> <nome>"));
	console.log("   Adiciona um item compartilhado ao projeto");
	console.log("   Tipos disponíveis:");
	console.log("   - component : Cria um componente React reutilizável");
	console.log("   - hook     : Cria um hook personalizado");
	console.log("   - util     : Cria uma função utilitária");
	console.log("   Exemplos:");
	console.log("   next-gen shared component Button");
	console.log("   next-gen shared hook useAuth");
	console.log("   next-gen shared util formatDate");
	console.log("");

	// I18n Command
	console.log(purpleGradient("🌐 i18n"));
	console.log("   Configura internacionalização (i18n) para o projeto");
	console.log("   Exemplo: next-gen i18n");
	console.log("   - Configuração interativa de idiomas");
	console.log("   - Setup de diretórios de tradução");
	console.log("   - Geração de arquivos de configuração");
	console.log("");

	// List Command
	console.log(purpleGradient("📋 list"));
	console.log("   Exibe esta lista de comandos com exemplos");
	console.log("   Exemplo: next-gen list");
	console.log("");

	console.log(greenGradient("💡 Dica: Todos os comandos aceitam --help para mais informações\n"));
}
