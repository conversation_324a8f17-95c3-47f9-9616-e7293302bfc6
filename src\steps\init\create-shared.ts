import { NamingConvention, applyNamingConvention } from "../../utils/applyNamingConvention.js";
import { FileUtils } from "../../utils/file-utils.js";

const fileUtils = new FileUtils();
const { createDir, createFile } = fileUtils;

interface SharedConfig {
	namingConvention: NamingConvention;
}

export function createShared(config: SharedConfig): void {
	createDir("src/shared/components");
	const buttonName = applyNamingConvention("button", config.namingConvention);
	createFile(
		`src/shared/components/${buttonName}.tsx`,
		`import React, { ReactNode } from 'react';\n\n` +
			`type Props = { children: ReactNode };\n` +
			`export const Button = ({ children }: Props) => <button>{children}</button>;`
	);
	createDir("src/shared/hooks");
	createDir("src/shared/utils");
}
