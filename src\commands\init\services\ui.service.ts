import gradient from "gradient-string";
import ora, { Ora } from "ora";

export interface IUIService {
	showSpinner(text: string): Ora;
	updateSpinnerText(spinner: Ora, text: string): void;
	completeStep(spinner: Ora, text: string): void;
	showSuccessMessage(): void;
	showCancelMessage(): void;
}

export class UIService implements IUIService {
	private readonly bluePurpleGradient = gradient(["#6B48FF", "#DDA0DD"]);
	private readonly tealGreenGradient = gradient(["#1ABC9C", "#27AE60"]);
	private readonly pastelGradient = gradient(["#F4A261", "#E76F51"]);

	showSpinner(text: string): Ora {
		return ora({
			text: this.pastelGradient(text),
			spinner: "star",
		}).start();
	}

	updateSpinnerText(spinner: Ora, text: string): void {
		spinner.text = this.pastelGradient(text);
	}

	completeStep(spinner: Ora, text: string): void {
		spinner.succeed(this.tealGreenGradient(text));
	}

	showSuccessMessage(): void {
		console.log(this.bluePurpleGradient("╔════════════════════════════════════╗"));
		console.log(this.bluePurpleGradient("║ 🎉 Projeto Configurado com Sucesso!║"));
		console.log(this.bluePurpleGradient("╚════════════════════════════════════╝"));
	}

	showCancelMessage(): void {
		console.log("Operação cancelada.");
	}
}
