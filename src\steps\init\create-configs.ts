import { FileUtils } from "../../utils/file-utils.js";

const fileUtils = new FileUtils();
const { createFile } = fileUtils;

export function createConfigs(): void {
	try {
		createFile(
			"src/config/app-config.ts",
			`export const appConfig = {
  name: 'next-app',
  version: '1.0.0',
  environment: process.env.NODE_ENV || 'development'
};`
		);

		createFile(
			"src/config/api-config.ts",
			`export const apiConfig = {
  baseUrl: process.env.API_URL || 'http://localhost:3000',
  timeout: 5000
};`
		);

		createFile(
			"src/config/theme-config.ts",
			`export const themeConfig = {
  primaryColor: '#007bff',
  secondaryColor: '#6c757d'
};`
		);
	} catch (error) {
		const message = error instanceof Error ? error.message : String(error);
		throw new Error(`Failed to create config files: ${message}`);
	}
}
