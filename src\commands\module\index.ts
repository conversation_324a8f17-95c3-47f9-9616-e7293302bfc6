import { FileUtils } from "../../utils/file-utils.js";
import { StepExecutor } from "../init/services/step-executor.service.js";
import { UIService } from "../init/services/ui.service.js";
import { ModuleConfigService } from "./services/module-config.service.js";

class ModuleCommand {
	constructor(
		private readonly moduleConfigService: ModuleConfigService,
		private readonly uiService: UIService,
		private readonly stepExecutor: StepExecutor,
		private readonly moduleName: string
	) {}

	async execute(): Promise<void> {
		const moduleConfig = await this.moduleConfigService.promptForModuleConfig(this.moduleName);
		const fileUtils = new FileUtils();
		const steps = [
			{
				name: "Criando estrutura do módulo",
				action: () => {
					const basePath = `${moduleConfig.path}/${moduleConfig.name}`;
					fileUtils.createDir(basePath);
					if (moduleConfig.includeComponents) fileUtils.createDir(`${basePath}/components`);
					if (moduleConfig.includeHooks) fileUtils.createDir(`${basePath}/hooks`);
					if (moduleConfig.includeServices) fileUtils.createDir(`${basePath}/services`);
				},
			},
		];
		await this.stepExecutor.executeSteps(steps);
		this.uiService.showSuccessMessage();
	}
}

const moduleCommand = async (name: string): Promise<void> => {
	const moduleConfigService = new ModuleConfigService();
	const uiService = new UIService();
	const stepExecutor = new StepExecutor(uiService);
	const command = new ModuleCommand(moduleConfigService, uiService, stepExecutor, name);
	return command.execute();
};

export default moduleCommand;
