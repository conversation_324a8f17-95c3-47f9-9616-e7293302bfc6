import { getInitSteps } from "../../steps/init/index.js";
import { FileUtils } from "../../utils/file-utils.js";
import { ConfigService } from "./services/config.service.js";
import { StepExecutor } from "./services/step-executor.service.js";
import { UIService } from "./services/ui.service.js";

class InitCommand {
	constructor(private readonly configService: ConfigService, private readonly uiService: UIService, private readonly stepExecutor: StepExecutor) {}

	async execute(): Promise<void> {
		const configFilePath = "nsga-config.json";
		if (await this.configService.checkConfigExists(configFilePath)) {
			const shouldOverride = await this.configService.promptForOverride();
			if (!shouldOverride) {
				this.uiService.showCancelMessage();
				return;
			}
		}

		const configAnswers = await this.configService.promptForConfig();
		const configFileContent = JSON.stringify(configAnswers, null, 2);
		const steps = getInitSteps(configAnswers);

		await this.stepExecutor.executeSteps(steps);
		this.configService.saveConfig(configFilePath, configFileContent);
		this.uiService.showSuccessMessage();
	}
}

const initCommand = async () => {
	const fileUtils = new FileUtils();
	const configService = new ConfigService(fileUtils);
	const uiService = new UIService();
	const stepExecutor = new StepExecutor(uiService);
	const command = new InitCommand(configService, uiService, stepExecutor);
	await command.execute();
};

export default initCommand;
