import fs from "fs";
import inquirer from "inquirer";
import { FileUtils } from "../../../utils/file-utils.js";
import { ConfigAnswers } from "../types.js";

export interface IConfigService {
	checkConfigExists(path: string): Promise<boolean>;
	promptForOverride(): Promise<boolean>;
	promptForConfig(): Promise<ConfigAnswers>;
	saveConfig(path: string, content: string): void;
}

export class ConfigService implements IConfigService {
	constructor(private readonly fileUtils: FileUtils) {}

	async checkConfigExists(path: string): Promise<boolean> {
		return fs.existsSync(path);
	}

	async promptForOverride(): Promise<boolean> {
		const { override } = await inquirer.prompt([
			{
				type: "confirm",
				name: "override",
				message: `O arquivo nsga-config.json já existe. Deseja sobrescrever?`,
				default: false,
			},
		]);
		return override;
	}

	async promptForConfig(): Promise<ConfigAnswers> {
		return await inquirer.prompt<ConfigAnswers>([
			{
				type: "list",
				name: "namingConvention",
				message: "Qual convenção de nomenclatura você prefere?",
				choices: ["camelCase", "kebab-case", "snake_case", "PascalCase"],
				default: "kebab-case",
			},
			{
				type: "confirm",
				name: "includeFeatures",
				message: "Incluir pasta features (auth, etc)?",
				default: true,
			},
			{
				type: "confirm",
				name: "includeShared",
				message: "Incluir pasta shared (components, hooks)?",
				default: true,
			},
		]);
	}

	saveConfig(path: string, content: string): void {
		this.fileUtils.createFile(path, content);
	}
}
