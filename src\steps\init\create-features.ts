import { NamingConvention, applyNamingConvention } from "../../utils/applyNamingConvention.js";
import { FileUtils } from "../../utils/file-utils.js";

const fileUtils = new FileUtils();
const { createDir, createFile } = fileUtils;

interface CreateFeaturesConfig {
	namingConvention: NamingConvention;
}

export function createFeatures(config: CreateFeaturesConfig): void {
	createDir("src/features/auth");
	const authProviderName = applyNamingConvention("auth-provider", config.namingConvention);
	createFile(
		`src/features/auth/${authProviderName}.tsx`,
		`import React, { ReactNode } from 'react';\n\n` +
			`type Props = { children: ReactNode };\n` +
			`export const AuthProvider = ({ children }: Props) => <>{children}</>;`
	);
	const useAuthName = applyNamingConvention("use-auth", config.namingConvention);
	createFile(`src/features/auth/${useAuthName}.ts`, `export const useAuth = () => ({});`);
}
