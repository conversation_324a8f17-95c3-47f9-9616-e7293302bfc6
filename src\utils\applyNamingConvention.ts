export type NamingConvention = "camelCase" | "kebab-case" | "snake_case" | "PascalCase";

export const applyNamingConvention = (name: string, convention: NamingConvention): string => {
	switch (convention) {
		case "camelCase":
			return name;
		case "kebab-case":
			return name.replace(/([A-Z])/g, "-$1").toLowerCase();
		case "snake_case":
			return name.replace(/([A-Z])/g, "_$1").toLowerCase();
		case "PascalCase":
			return name.charAt(0).toUpperCase() + name.slice(1);
		default:
			return name;
	}
};
