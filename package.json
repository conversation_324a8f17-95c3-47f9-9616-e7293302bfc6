{"name": "next-structure-generator", "version": "1.0.0", "type": "module", "description": "CLI para gerar estruturas de pastas para projetos Next.js", "main": "dist/index.js", "bin": {"next-gen": "./dist/bin/cli.js"}, "scripts": {"build": "tsc --build --clean && tsc && chmod +x ./dist/bin/cli.js", "start": "node dist/bin/cli.js", "dev": "ts-node --esm src/bin/cli.ts", "lint": "eslint . --ext .ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["nextjs", "cli", "structure", "generator"], "author": "<PERSON><PERSON>", "license": "MIT", "dependencies": {"axios": "^1.7.7", "cfonts": "^3.3.0", "chalk": "^5.4.1", "chalk-animation": "^2.0.3", "commander": "^12.1.0", "figlet": "^1.8.0", "fs-extra": "^11.3.0", "gradient-string": "^3.0.0", "inquirer": "^12.5.0", "ora": "^8.2.0"}, "devDependencies": {"@types/figlet": "^1.7.0", "@types/fs-extra": "^11.0.4", "@types/node": "^22.7.5", "ts-node": "^10.9.2", "typescript": "^5.6.3", "eslint": "^9.13.0", "@typescript-eslint/eslint-plugin": "^8.11.0", "@typescript-eslint/parser": "^8.11.0"}, "engines": {"node": ">=18.0.0"}}