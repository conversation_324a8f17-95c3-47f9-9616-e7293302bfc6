{"compilerOptions": {"target": "ESNext", "module": "NodeNext", "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "NodeNext", "noImplicitAny": true, "noUnusedLocals": true, "noUnusedParameters": true, "sourceMap": true, "resolveJsonModule": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}