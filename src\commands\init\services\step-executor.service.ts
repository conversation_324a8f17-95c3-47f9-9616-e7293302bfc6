import { IUIService } from "./ui.service.js";

export interface IStepExecutor {
	executeSteps(steps: Array<{ name: string; action: () => void }>): Promise<void>;
}

export class StepExecutor implements IStepExecutor {
	constructor(private readonly uiService: IUIService) {}

	private sleep(ms: number): Promise<void> {
		return new Promise<void>((resolve: () => void) => setTimeout(resolve, ms));
	}

	async executeSteps(steps: Array<{ name: string; action: () => void }>): Promise<void> {
		const spinner = this.uiService.showSpinner("⚙️ Iniciando configuração...");
		for (let i = 0; i < steps.length; i++) {
			this.uiService.updateSpinnerText(spinner, `[${i + 1}/${steps.length}] ${steps[i].name}...`);
			await this.sleep(800);
			steps[i].action();
			this.uiService.completeStep(spinner, `[${i + 1}/${steps.length}] ${steps[i].name} concluído! ✅`);
			if (i < steps.length - 1) spinner.start();
		}

		spinner.stop();
	}
}
