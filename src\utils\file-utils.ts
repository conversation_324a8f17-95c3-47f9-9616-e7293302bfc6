#!/usr/bin/env node

import fsExtra from "fs-extra";
import path from "path";

export class FileUtils {
	private readonly fs: typeof fsExtra;
	private readonly path: typeof path;

	constructor(fsModule = fsExtra, pathModule = path) {
		this.fs = fsModule || fsExtra;
		this.path = pathModule || path;
	}

	public createDir = (dirPath: string): void => {
		try {
			if (!this.fs) {
			}
			this.fs.ensureDirSync(dirPath);
		} catch (error) {
			throw new Error(`Unable to create directory "${dirPath}": ${error}`);
		}
	};

	public createFile = (filePath: string, content: string = ""): void => {
		try {
			if (!this.fs || !this.path) {
				throw new Error("File system or path module is not available");
			}
			const dir = this.path.dirname(filePath);
			this.fs.ensureDirSync(dir);
			this.fs.writeFileSync(filePath, content);
		} catch (error) {
			throw new Error(`Unable to create file "${filePath}": ${error}`);
		}
	};
}
