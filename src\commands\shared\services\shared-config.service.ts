import inquirer from "inquirer";
import type { SharedConfig } from "../types.js";

export interface ISharedConfigService {
	promptForSharedConfig(): Promise<SharedConfig>;
}

export class SharedConfigService implements ISharedConfigService {
	async promptForSharedConfig(): Promise<SharedConfig> {
		const typeQuestion = [
			{
				type: "list" as const,
				name: "type" as const,
				message: "Qual tipo de item compartilhado você quer criar?",
				choices: [
					{ name: "Component", value: "component" },
					{ name: "Hook", value: "hook" },
					{ name: "Service", value: "service" },
				],
			},
		];

		type TypeAnswer = { type: "component" | "hook" | "service" };
		const { type } = await inquirer.prompt<TypeAnswer>(typeQuestion);

		const baseQuestions = [
			{
				type: "input" as const,
				name: "name" as const,
				message: `Nome do ${type}:`,
				validate: (input: string) => input.length > 0,
			},
			{
				type: "confirm" as const,
				name: "withTest" as const,
				message: "Criar arquivo de teste?",
				default: true,
			},
		];

		const answers = await inquirer.prompt(baseQuestions);

		if (type === "component") {
			const storybookQuestion = [
				{
					type: "confirm" as const,
					name: "withStorybook" as const,
					message: "Criar arquivo Storybook?",
					default: true,
				},
			];

			const { withStorybook } = await inquirer.prompt(storybookQuestion);
			return { type, ...answers, withStorybook };
		}

		return { type, ...answers };
	}
}
