import { NamingConvention, applyNamingConvention } from "../../utils/applyNamingConvention.js";
import { FileUtils } from "../../utils/file-utils.js";

const fileUtils = new FileUtils();
const { createDir, createFile } = fileUtils;

interface LayoutConfig {
	namingConvention: NamingConvention;
}

export function createLayout(config: LayoutConfig): void {
	createDir("src/layout/components");
	const headerName = applyNamingConvention("header", config.namingConvention);
	createFile(`src/layout/components/${headerName}.tsx`, `import React from 'react';\n\n` + `export const Header = () => <header>Header</header>;`);
}
