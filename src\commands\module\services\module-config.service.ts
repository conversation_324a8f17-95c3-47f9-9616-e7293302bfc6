import inquirer from "inquirer";
import { ModuleConfig } from "../types.js";

export interface IModuleConfigService {
	promptForModuleConfig(moduleName?: string): Promise<ModuleConfig>;
}

export class ModuleConfigService implements IModuleConfigService {
	async promptForModuleConfig(moduleName?: string): Promise<ModuleConfig> {
		interface Answers extends Omit<ModuleConfig, "name"> {
			name?: string;
		}

		const questions = [
			...(moduleName
				? []
				: [
						{
							type: "input" as const,
							name: "name" as const,
							message: "Qual o nome do módulo?",
							validate: (input: string) => input.length > 0,
						},
				  ]),
			{
				type: "input" as const,
				name: "path" as const,
				message: "Qual o caminho do módulo? (ex: src/features)",
				default: "src/features",
			},
			{
				type: "confirm" as const,
				name: "includeComponents" as const,
				message: "Incluir pasta components?",
				default: true,
			},
			{
				type: "confirm" as const,
				name: "includeHooks" as const,
				message: "Incluir pasta hooks?",
				default: true,
			},
			{
				type: "confirm" as const,
				name: "includeServices" as const,
				message: "Incluir pasta services?",
				default: true,
			},
		];

		const answers = await inquirer.prompt<Answers>(questions);
		return {
			...answers,
			name: moduleName ?? answers.name ?? "",
		};
	}
}
