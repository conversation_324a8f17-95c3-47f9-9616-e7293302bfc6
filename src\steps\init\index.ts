import { ConfigAnswers } from "../../commands/init/types.js";
import { createConfigs } from "./create-configs.js";
import { createFeatures } from "./create-features.js";
import { createLayout } from "./create-layout.js";
import { createRootDirs } from "./create-root-dir.js";
import { createShared } from "./create-shared.js";

export interface Step {
	name: string;
	action: () => void;
}

export function getInitSteps(configAnswers: ConfigAnswers): Step[] {
	const { includeFeatures, includeShared } = configAnswers;
	return [
		{ name: "📁 Criando diretórios raiz", action: createRootDirs },
		{ name: "⚙️ Gerando configurações", action: createConfigs },
		...(includeFeatures ? [{ name: "🔒 Configurando features", action: () => createFeatures(configAnswers) }] : []),
		{ name: "🖼️ Montando layout", action: () => createLayout(configAnswers) },
		...(includeShared ? [{ name: "🌐 Adicionando shared", action: () => createShared(configAnswers) }] : []),
	];
}
