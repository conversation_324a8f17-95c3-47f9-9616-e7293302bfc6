#!/usr/bin/env node

import { program } from "commander";
import figlet from "figlet";
import gradient from "gradient-string";
import featureCommand from "../commands/feature/index.js";
import i18nCommand from "../commands/i18n/index.js";
import initCommand from "../commands/init/index.js";
import listCommand from "../commands/list/index.js";
import moduleCommand from "../commands/module/index.js";
import sharedCommand from "../commands/shared/index.js";
import { SharedItemType } from "../commands/shared/types.js";

const CLI_VERSION = "1.0.0";
const CLI_NAME = "Next Gen";

class CLI {
	private readonly bluePurpleGradient = gradient(["#6B48FF", "#DDA0DD"]);
	private readonly tealGreenGradient = gradient(["#1ABC9C", "#27AE60"]);

	private welcomeBanner(): void {
		const banner = figlet.textSync(CLI_NAME, { font: "Cyberlarge" });
		console.log(this.bluePurpleGradient(banner));
		console.log(this.tealGreenGradient(`🌌 Structure Generator v${CLI_VERSION} 🌠\n`));
	}

	private registerCommands(): void {
		program.version(CLI_VERSION).description("CLI moderna para gerar estruturas de pastas para Next.js");

		program
			.command("init")
			.description("Cria a estrutura completa do projeto com configurações personalizadas")
			.action(async () => {
				this.welcomeBanner();
				await initCommand();
			});

		program
			.command("module <name>")
			.description("Cria um novo módulo com componentes, hooks e serviços")
			.action(async name => {
				this.welcomeBanner();
				await moduleCommand(name);
			});

		program
			.command("feature <name>")
			.description("Cria uma nova feature com componentes, hooks, serviços, estilos e testes")
			.action(async name => {
				this.welcomeBanner();
				await featureCommand(name);
			});

		program
			.command("shared <type> <name>")
			.description("Adiciona um item compartilhado (component/hook/util)")
			.action(async (type, name) => {
				if (!["component", "hook", "util"].includes(type)) {
					console.error("Erro: tipo deve ser: component, hook ou util");
					process.exit(1);
				}
				this.welcomeBanner();
				await sharedCommand(type as SharedItemType, name);
			});

		program
			.command("list")
			.description("Lista todos os comandos disponíveis com exemplos")
			.action(async () => {
				this.welcomeBanner();
				await listCommand();
			});

		program
			.command("i18n")
			.description("Configura internacionalização (i18n) para o projeto")
			.action(async () => {
				this.welcomeBanner();
				await i18nCommand();
			});
	}

	public run(): void {
		this.registerCommands();

		if (!process.argv.slice(2).length) {
			this.welcomeBanner();
			program.outputHelp();
		}

		program.parse(process.argv);
	}
}

const cli = new CLI();
cli.run();
