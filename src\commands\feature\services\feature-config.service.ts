import inquirer from "inquirer";
import { FeatureConfig } from "../types.js";

export interface IFeatureConfigService {
	promptForFeatureConfig(featureName?: string): Promise<FeatureConfig>;
}

export class FeatureConfigService implements IFeatureConfigService {
	async promptForFeatureConfig(featureName?: string): Promise<FeatureConfig> {
		interface Answers extends Omit<FeatureConfig, "name"> {
			name?: string;
		}

		const questions = [
			...(featureName
				? []
				: [
						{
							type: "input" as const,
							name: "name" as const,
							message: "Qual o nome da feature?",
							validate: (input: string) => input.length > 0,
						},
				  ]),
			{
				type: "input" as const,
				name: "path" as const,
				message: "Qual o caminho da feature? (ex: src/features)",
				default: "src/features",
			},
			{
				type: "confirm" as const,
				name: "includeComponents" as const,
				message: "Incluir pasta components?",
				default: true,
			},
			{
				type: "confirm" as const,
				name: "includeHooks" as const,
				message: "Incluir pasta hooks?",
				default: true,
			},
			{
				type: "confirm" as const,
				name: "includeServices" as const,
				message: "Incluir pasta services?",
				default: true,
			},
			{
				type: "confirm" as const,
				name: "includeStyles" as const,
				message: "Incluir pasta styles?",
				default: true,
			},
			{
				type: "confirm" as const,
				name: "includeTests" as const,
				message: "Incluir pasta __tests__?",
				default: true,
			},
		];

		const answers = await inquirer.prompt<Answers>(questions);
		return {
			...answers,
			name: featureName ?? answers.name ?? "",
		};
	}
}
