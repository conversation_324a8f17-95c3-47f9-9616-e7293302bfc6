import inquirer from "inquirer";
import { I18nConfig } from "../types.js";

export interface II18nConfigService {
	promptForI18nConfig(): Promise<I18nConfig>;
}

export class I18nConfigService implements II18nConfigService {
	async promptForI18nConfig(): Promise<I18nConfig> {
		const questions = [
			{
				type: "input" as const,
				name: "defaultLanguage" as const,
				message: "Qual o idioma padrão? (ex: pt-BR)",
				default: "pt-BR",
			},
			{
				type: "input" as const,
				name: "languages" as const,
				message: "Quais idiomas adicionais? (separados por vírgula, ex: en,es,fr)",
				filter: (input: string) =>
					input
						.split(",")
						.map(lang => lang.trim())
						.filter(Boolean),
			},
			{
				type: "input" as const,
				name: "namespaces" as const,
				message: "Quais namespaces de tradução? (separados por vírgula, ex: common,auth,errors)",
				default: "common",
				filter: (input: string) =>
					input
						.split(",")
						.map(ns => ns.trim())
						.filter(Boolean),
			},
			{
				type: "input" as const,
				name: "path" as const,
				message: "Qual o caminho para os arquivos de tradução?",
				default: "src/locales",
			},
		];

		return inquirer.prompt<I18nConfig>(questions);
	}
}
